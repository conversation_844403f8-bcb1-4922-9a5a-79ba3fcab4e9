package ca.nbc.payment;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.DefaultValue;
import org.springframework.stereotype.Component;

import java.util.Map;

@ConfigurationProperties(prefix = "dynamodb")
@Component
public record DynamoDbConfig(Map<String, TableConfig> tables) {

    public record TableConfig(
            @DefaultValue("") String endpoint,
            String region,
            String tableName,
            @DefaultValue("3000") int timeout,
            @DefaultValue("") String profile,
            @DefaultValue("NONE") String retryPolicy) {
    }
}
